package database

import (
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"strings"

	"github.com/izy-mercado/backend/internal/config"
	"github.com/izy-mercado/backend/internal/logger"
)

// SSLConfig holds SSL configuration for PostgreSQL connections
type SSLConfig struct {
	Mode       string
	ServerCA   string
	ClientCert string
	ClientKey  string
}

// CreateTLSConfig creates a TLS configuration from SSL certificates stored as raw text
func CreateTLSConfig(sslConfig SSLConfig, log logger.Logger) (*tls.Config, error) {
	if sslConfig.Mode == "" || sslConfig.Mode == "disable" {
		log.WithFields(map[string]interface{}{
			"component": "database_ssl",
			"action":    "ssl_disabled",
		}).Info("SSL disabled for database connection")
		return nil, nil
	}

	tlsConfig := &tls.Config{}

	// Configure SSL mode
	switch sslConfig.Mode {
	case "require":
		tlsConfig.InsecureSkipVerify = true
		log.WithFields(map[string]interface{}{
			"component": "database_ssl",
			"ssl_mode":  "require",
		}).Info("SSL mode: require (no certificate verification)")
	case "verify-ca":
		// For verify-ca mode, we skip hostname verification but verify the certificate
		tlsConfig.InsecureSkipVerify = true
		log.WithFields(map[string]interface{}{
			"component": "database_ssl",
			"ssl_mode":  "verify-ca",
		}).Info("SSL mode: verify-ca (server certificate verification)")
	case "verify-full":
		tlsConfig.InsecureSkipVerify = false
		log.WithFields(map[string]interface{}{
			"component": "database_ssl",
			"ssl_mode":  "verify-full",
		}).Info("SSL mode: verify-full (full certificate verification)")
	default:
		return nil, fmt.Errorf("unsupported SSL mode: %s", sslConfig.Mode)
	}

	// Configure server CA certificate if provided
	if sslConfig.ServerCA != "" {
		caCertPool := x509.NewCertPool()

		// Clean up the certificate content (remove extra whitespace, ensure proper formatting)
		serverCA := strings.TrimSpace(sslConfig.ServerCA)
		if !strings.HasPrefix(serverCA, "-----BEGIN CERTIFICATE-----") {
			return nil, fmt.Errorf("server CA certificate must be in PEM format")
		}

		if !caCertPool.AppendCertsFromPEM([]byte(serverCA)) {
			return nil, fmt.Errorf("failed to parse server CA certificate")
		}

		tlsConfig.RootCAs = caCertPool

		// For verify-ca mode with Cloud SQL, we need custom verification
		if sslConfig.Mode == "verify-ca" {
			tlsConfig.VerifyPeerCertificate = func(rawCerts [][]byte, verifiedChains [][]*x509.Certificate) error {
				// Parse the server certificate
				if len(rawCerts) == 0 {
					return fmt.Errorf("no certificates provided by server")
				}

				cert, err := x509.ParseCertificate(rawCerts[0])
				if err != nil {
					return fmt.Errorf("failed to parse server certificate: %w", err)
				}

				// Verify against our CA
				roots := x509.NewCertPool()
				roots.AppendCertsFromPEM([]byte(serverCA))

				opts := x509.VerifyOptions{
					Roots: roots,
				}

				_, err = cert.Verify(opts)
				if err != nil {
					return fmt.Errorf("certificate verification failed: %w", err)
				}

				return nil
			}
		}

		log.WithFields(map[string]interface{}{
			"component": "database_ssl",
			"action":    "server_ca_loaded",
		}).Info("Server CA certificate loaded successfully")
	}

	// Configure client certificate and key if provided
	if sslConfig.ClientCert != "" && sslConfig.ClientKey != "" {
		// Clean up certificate and key content
		clientCert := strings.TrimSpace(sslConfig.ClientCert)
		clientKey := strings.TrimSpace(sslConfig.ClientKey)

		if !strings.HasPrefix(clientCert, "-----BEGIN CERTIFICATE-----") {
			return nil, fmt.Errorf("client certificate must be in PEM format")
		}

		if !strings.HasPrefix(clientKey, "-----BEGIN") {
			return nil, fmt.Errorf("client key must be in PEM format")
		}

		cert, err := tls.X509KeyPair([]byte(clientCert), []byte(clientKey))
		if err != nil {
			log.WithFields(map[string]interface{}{
				"component": "database_ssl",
				"action":    "client_cert_load_failed",
				"error":     err.Error(),
			}).Error("Failed to load client certificate and key")
			return nil, fmt.Errorf("failed to load client certificate and key: %w", err)
		}

		tlsConfig.Certificates = []tls.Certificate{cert}
		log.WithFields(map[string]interface{}{
			"component": "database_ssl",
			"action":    "client_cert_loaded",
		}).Info("Client certificate and key loaded successfully")
	} else if sslConfig.ClientCert != "" || sslConfig.ClientKey != "" {
		return nil, fmt.Errorf("both client certificate and client key must be provided together")
	}

	log.WithFields(map[string]interface{}{
		"component":          "database_ssl",
		"action":             "tls_config_created",
		"ssl_mode":           sslConfig.Mode,
		"has_server_ca":      sslConfig.ServerCA != "",
		"has_client_cert":    sslConfig.ClientCert != "",
		"skip_verify":        tlsConfig.InsecureSkipVerify,
		"certificates_count": len(tlsConfig.Certificates),
	}).Info("TLS configuration created successfully")

	return tlsConfig, nil
}

// NewSSLConfigFromEnv creates SSL configuration from environment variables
// Always uses "verify-ca" mode when server CA is provided
func NewSSLConfigFromEnv(dbConfig config.Database) SSLConfig {
	mode := ""
	if dbConfig.SSLServerCA != "" {
		mode = "verify-ca"
	}

	return SSLConfig{
		Mode:       mode,
		ServerCA:   dbConfig.SSLServerCA,
		ClientCert: dbConfig.SSLClientCert,
		ClientKey:  dbConfig.SSLClientKey,
	}
}
