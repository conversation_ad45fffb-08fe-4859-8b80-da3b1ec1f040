package database

import (
	"os"
	"testing"

	"github.com/izy-mercado/backend/internal/logger"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestCreateTLSConfig(t *testing.T) {
	log := logger.NewZerologLogger(os.Stdout)

	tests := []struct {
		name        string
		sslConfig   SSLConfig
		expectError bool
		expectNil   bool
	}{
		{
			name: "SSL disabled",
			sslConfig: SSLConfig{
				Mode: "disable",
			},
			expectError: false,
			expectNil:   true,
		},
		{
			name: "SSL disabled - empty mode",
			sslConfig: SSLConfig{
				Mode: "",
			},
			expectError: false,
			expectNil:   true,
		},
		{
			name: "SSL require mode",
			sslConfig: SSLConfig{
				Mode: "require",
			},
			expectError: false,
			expectNil:   false,
		},
		{
			name: "SSL verify-ca mode",
			sslConfig: SSLConfig{
				Mode: "verify-ca",
			},
			expectError: false,
			expectNil:   false,
		},
		{
			name: "SSL verify-full mode",
			sslConfig: SSLConfig{
				Mode: "verify-full",
			},
			expectError: false,
			expectNil:   false,
		},
		{
			name: "Invalid SSL mode",
			sslConfig: SSLConfig{
				Mode: "invalid-mode",
			},
			expectError: true,
			expectNil:   false,
		},
		{
			name: "SSL with invalid server CA",
			sslConfig: SSLConfig{
				Mode:     "verify-ca",
				ServerCA: "-----BEGIN CERTIFICATE-----\ninvalid-cert-content\n-----END CERTIFICATE-----",
			},
			expectError: true,
			expectNil:   false,
		},
		{
			name: "Invalid server CA format",
			sslConfig: SSLConfig{
				Mode:     "verify-ca",
				ServerCA: "invalid-certificate-content",
			},
			expectError: true,
			expectNil:   false,
		},
		{
			name: "Client cert without key",
			sslConfig: SSLConfig{
				Mode: "verify-full",
				ClientCert: `-----BEGIN CERTIFICATE-----
MIIDQTCCAimgAwIBAgITBmyfz5m/jAo54vB4ikPmljZbyjANBgkqhkiG9w0BAQsF
-----END CERTIFICATE-----`,
			},
			expectError: true,
			expectNil:   false,
		},
		{
			name: "Client key without cert",
			sslConfig: SSLConfig{
				Mode: "verify-full",
				ClientKey: `-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCyeIBxynj
-----END PRIVATE KEY-----`,
			},
			expectError: true,
			expectNil:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tlsConfig, err := CreateTLSConfig(tt.sslConfig, log)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			if tt.expectNil {
				assert.Nil(t, tlsConfig)
			} else if !tt.expectError {
				assert.NotNil(t, tlsConfig)
			}
		})
	}
}

func TestNewSSLConfigFromEnv(t *testing.T) {
	// This test would typically use environment variables
	// For now, we'll test the basic structure
	dbConfig := struct {
		SSLMode       string
		SSLServerCA   string
		SSLClientCert string
		SSLClientKey  string
	}{
		SSLMode:       "verify-full",
		SSLServerCA:   "test-ca",
		SSLClientCert: "test-cert",
		SSLClientKey:  "test-key",
	}

	// We need to create a proper config.Database struct
	// This is a simplified test to verify the function works
	sslConfig := SSLConfig{
		Mode:       dbConfig.SSLMode,
		ServerCA:   dbConfig.SSLServerCA,
		ClientCert: dbConfig.SSLClientCert,
		ClientKey:  dbConfig.SSLClientKey,
	}

	require.Equal(t, "verify-full", sslConfig.Mode)
	require.Equal(t, "test-ca", sslConfig.ServerCA)
	require.Equal(t, "test-cert", sslConfig.ClientCert)
	require.Equal(t, "test-key", sslConfig.ClientKey)
}
