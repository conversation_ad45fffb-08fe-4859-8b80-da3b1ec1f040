package database

import (
	"os"
	"testing"

	"github.com/izy-mercado/backend/internal/logger"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestCreateTLSConfig(t *testing.T) {
	log := logger.NewZerologLogger(os.Stdout)

	tests := []struct {
		name        string
		sslConfig   SSLConfig
		expectError bool
		expectNil   bool
	}{
		{
			name: "SSL disabled",
			sslConfig: SSLConfig{
				Mode: "disable",
			},
			expectError: false,
			expectNil:   true,
		},
		{
			name: "SSL disabled - empty mode",
			sslConfig: SSLConfig{
				Mode: "",
			},
			expectError: false,
			expectNil:   true,
		},
		{
			name: "SSL require mode",
			sslConfig: SSLConfig{
				Mode: "require",
			},
			expectError: false,
			expectNil:   false,
		},
		{
			name: "SSL verify-ca mode",
			sslConfig: SSLConfig{
				Mode: "verify-ca",
			},
			expectError: false,
			expectNil:   false,
		},
		{
			name: "SSL verify-full mode",
			sslConfig: SSLConfig{
				Mode: "verify-full",
			},
			expectError: false,
			expectNil:   false,
		},
		{
			name: "Invalid SSL mode",
			sslConfig: SSLConfig{
				Mode: "invalid-mode",
			},
			expectError: true,
			expectNil:   false,
		},
		{
			name: "SSL with invalid server CA",
			sslConfig: SSLConfig{
				Mode:     "verify-ca",
				ServerCA: "-----BEGIN CERTIFICATE-----\ninvalid-cert-content\n-----END CERTIFICATE-----",
			},
			expectError: true,
			expectNil:   false,
		},
		{
			name: "Invalid server CA format",
			sslConfig: SSLConfig{
				Mode:     "verify-ca",
				ServerCA: "invalid-certificate-content",
			},
			expectError: true,
			expectNil:   false,
		},
		{
			name: "Client cert without key",
			sslConfig: SSLConfig{
				Mode: "verify-full",
				ClientCert: `-----BEGIN CERTIFICATE-----
MIIDQTCCAimgAwIBAgITBmyfz5m/jAo54vB4ikPmljZbyjANBgkqhkiG9w0BAQsF
-----END CERTIFICATE-----`,
			},
			expectError: true,
			expectNil:   false,
		},
		{
			name: "Client key without cert",
			sslConfig: SSLConfig{
				Mode: "verify-full",
				ClientKey: `-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCyeIBxynj
-----END PRIVATE KEY-----`,
			},
			expectError: true,
			expectNil:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tlsConfig, err := CreateTLSConfig(tt.sslConfig, log)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			if tt.expectNil {
				assert.Nil(t, tlsConfig)
			} else if !tt.expectError {
				assert.NotNil(t, tlsConfig)
			}
		})
	}
}

func TestNewSSLConfigFromEnv(t *testing.T) {
	tests := []struct {
		name         string
		serverCA     string
		clientCert   string
		clientKey    string
		expectedMode string
	}{
		{
			name:         "No SSL certificates",
			serverCA:     "",
			clientCert:   "",
			clientKey:    "",
			expectedMode: "",
		},
		{
			name:         "Server CA only",
			serverCA:     "test-ca-content",
			clientCert:   "",
			clientKey:    "",
			expectedMode: "verify-ca",
		},
		{
			name:         "Server CA with client certificates",
			serverCA:     "test-ca-content",
			clientCert:   "test-cert-content",
			clientKey:    "test-key-content",
			expectedMode: "verify-ca",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a mock config.Database struct
			dbConfig := struct {
				SSLServerCA   string
				SSLClientCert string
				SSLClientKey  string
			}{
				SSLServerCA:   tt.serverCA,
				SSLClientCert: tt.clientCert,
				SSLClientKey:  tt.clientKey,
			}

			// Test the SSL config creation logic
			mode := ""
			if dbConfig.SSLServerCA != "" {
				mode = "verify-ca"
			}

			sslConfig := SSLConfig{
				Mode:       mode,
				ServerCA:   dbConfig.SSLServerCA,
				ClientCert: dbConfig.SSLClientCert,
				ClientKey:  dbConfig.SSLClientKey,
			}

			require.Equal(t, tt.expectedMode, sslConfig.Mode)
			require.Equal(t, tt.serverCA, sslConfig.ServerCA)
			require.Equal(t, tt.clientCert, sslConfig.ClientCert)
			require.Equal(t, tt.clientKey, sslConfig.ClientKey)
		})
	}
}
