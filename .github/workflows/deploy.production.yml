name: Deploy from Release

on:
  release:
    types: [published]

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  REGION: us-central1
  SERVICE_NAME: izy-api
  GAR_NAME: images

jobs:
  deploy:
    name: Build, Push & Deploy
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Set Docker image tag from release
        id: set_tag
        run: |
          echo "Release Tag: ${{ github.event.release.tag_name }}"
          echo "tag=${{ github.event.release.tag_name }}" >> "$GITHUB_ENV"

      - name: Set up Google Cloud SDK
        uses: google-github-actions/auth@v2
        with:
          credentials_json: '${{ secrets.GCP_SA_KEY }}'
     
      - name: Configure Docker
        uses: docker/login-action@v2
        with:
          registry: us-central1-docker.pkg.dev
          username: _json_key
          password: ${{ secrets.GCP_SA_KEY }}

      - name: Build Docker image
        run: |
          # Enable Docker BuildKit
          export DOCKER_BUILDKIT=1
          docker build --target production \
            --build-arg GIT_TAG=${{ env.tag }} \
            -t ${{ env.REGION }}-docker.pkg.dev/${{ secrets.GCP_PROJECT_ID }}/${{ env.GAR_NAME }}/${{ env.SERVICE_NAME }}:${{ env.tag }} \
            -f Dockerfile .

      - name: Push Docker image to Artifact Registry
        run: |
          docker push ${{ env.REGION }}-docker.pkg.dev/${{ secrets.GCP_PROJECT_ID }}/${{ env.GAR_NAME }}/${{ env.SERVICE_NAME }}:${{ env.tag }}

      - name: Deploy to Cloud Run
        run: |
            gcloud run deploy ${{ env.SERVICE_NAME }} \
             --image ${{ env.REGION }}-docker.pkg.dev/${{ secrets.GCP_PROJECT_ID }}/${{ env.GAR_NAME }}/${{ env.SERVICE_NAME }}:${{ env.tag }}  \
             --region ${{ env.REGION }} \
             --min-instances 1 \
             --max-instances 1 \
             --memory 256M \
             --vpc-connector cloudsql-connector \
             --cpu 1 \
             --allow-unauthenticated \
             --update-env-vars APP_ENV=production,SWAGGER_HOST=api.izymercado.com.br,NEW_RELIC_APP_NAME=izy-api \
             --service-account <EMAIL> \
             --set-secrets BASIC_AUTH_PASSWORD=BASIC_AUTH_PASSWORD:latest,DATABASE_URL=DATABASE_URL:latest,JWT_SECRET=JWT_SECRET:latest,STORAGE_ACCOUNT_ID=STORAGE_ACCOUNT_ID:latest,STORAGE_ACCESS_KEY_ID=STORAGE_ACCESS_KEY_ID:latest,STORAGE_ACCESS_KEY_SECRET=STORAGE_ACCESS_KEY_SECRET:latest,STORAGE_BUCKET_NAME=STORAGE_BUCKET_NAME:latest,MAILER_TOKEN=MAILER_TOKEN:latest,MAILER_URL=MAILER_URL:latest,MAILER_SEND_CODE_TEMPLATE_KEY=MAILER_SEND_CODE_TEMPLATE_KEY:latest,MAILER_FROM=MAILER_FROM:latest,WOOVI_URL=WOOVI_URL:latest,WOOVI_API_KEY=WOOVI_API_KEY:latest,WOOVI_PIX_KEY=WOOVI_PIX_KEY:latest,NEW_RELIC_LICENSE_KEY=NEW_RELIC_LICENSE_KEY:latest,FCM_SERVICE_ACCOUNT_JSON=FCM_SERVICE_ACCOUNT_JSON:latest,FCM_VAPID_KEY=FCM_VAPID_KEY:latest,FCM_PROJECT_ID=FCM_PROJECT_ID:latest,DATABASE_SSL_MODE=DATABASE_SSL_MODE:latest,DATABASE_SSL_SERVER_CA=DATABASE_SSL_SERVER_CA:latest,DATABASE_SSL_CLIENT_CERT=DATABASE_SSL_CLIENT_CERT:latest,DATABASE_SSL_CLIENT_KEY=DATABASE_SSL_CLIENT_KEY:latest