#!/bin/bash

# Configurações
USER="postgres"
PASSWORD="r0vhodkly0"
IP="*************"
PORT="5432"
DB_NAME="izy-mercado-db"

# Data para diretório
DATA=$(date +"%d%m%Y")
DIRETORIO="./bkp_data_prod/$DATA"

# Criar diretório se não existir
mkdir -p "$DIRETORIO"

# Lista de tabelas
TABELAS=(
  "products_categories"
  "categories"
  "products"
  "subscriptions"
  "roles"
  "templates_products_lists"
  "templates_products_lists_items"
)

# Executa backup para cada tabela
for TABELA in "${TABELAS[@]}"; do
  pg_dump "postgres://${USER}:${PASSWORD}@${IP}:${PORT}/${DB_NAME}" \
    -t "$TABELA" \
    --data-only \
    --column-inserts \
    > "${DIRETORIO}/${TABELA}.sql"
done
