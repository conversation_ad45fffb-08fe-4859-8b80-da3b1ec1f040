# PostgreSQL SSL Configuration with Google Cloud Secret Manager

This document explains how to configure SSL connections to your Cloud SQL PostgreSQL instance using certificates stored in Google Cloud Secret Manager.

## Overview

The application supports secure SSL connections to PostgreSQL using:
- **Go Application**: Uses "verify-ca" mode with full certificate verification from raw certificate content
- **Migration Tools**: Uses "require" mode for SSL encryption (psql/migrate tools don't support raw certificates)
- Certificates loaded from Google Cloud Secret Manager as raw text (not file paths)

## SSL Mode Strategy

### Go Application (main.go)
- **Mode**: `verify-ca` with custom certificate verification
- **Certificates**: Uses raw certificate content from environment variables
- **Security**: Full certificate verification against provided CA

### Migration Script (migrate.sh)
- **Mode**: `require` for SSL encryption
- **Limitation**: psql and migrate tools don't support raw certificate content
- **Security**: SSL encryption enabled, but certificate verification is limited

## Setting up SSL Certificates in Google Cloud Secret Manager

### 1. Create the SSL Secrets

```bash
# Server CA Certificate (required for SSL connections)
gcloud secrets create DATABASE_SSL_SERVER_CA --data-file=server-ca.pem

# Client Certificate (optional, for client authentication)
gcloud secrets create DATABASE_SSL_CLIENT_CERT --data-file=client-cert.pem

# Client Private Key (required if client certificate is provided)
gcloud secrets create DATABASE_SSL_CLIENT_KEY --data-file=client-key.pem
```

### 2. Certificate Format Requirements

All certificates must be in PEM format:

**Server CA Certificate:**
```
-----BEGIN CERTIFICATE-----
MIIDQTCCAimgAwIBAgITBmyfz5m/jAo54vB4ikPmljZbyjANBgkqhkiG9w0BAQsF
...certificate content...
-----END CERTIFICATE-----
```

### 3. Obtaining Certificates from Cloud SQL

```bash
# Get the server CA certificate
gcloud sql ssl-certs describe server-ca-cert --instance=YOUR_INSTANCE_NAME --format="value(cert)" > server-ca.pem

# Create a client certificate (if needed)
gcloud sql ssl-certs create client-cert client-key.pem --instance=YOUR_INSTANCE_NAME

# Download the client certificate
gcloud sql ssl-certs describe client-cert --instance=YOUR_INSTANCE_NAME --format="value(cert)" > client-cert.pem
```

### 4. Grant Access to Secrets

```bash
# Grant access to all SSL secrets
for secret in DATABASE_SSL_SERVER_CA DATABASE_SSL_CLIENT_CERT DATABASE_SSL_CLIENT_KEY; do
  gcloud secrets add-iam-policy-binding $secret \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/secretmanager.secretAccessor"
done
```

## Configuration

Environment variables:
- `DATABASE_SSL_SERVER_CA`: Server CA certificate in PEM format (required for SSL)
- `DATABASE_SSL_CLIENT_CERT`: Client certificate in PEM format (optional)
- `DATABASE_SSL_CLIENT_KEY`: Client private key in PEM format (optional)

## Local Development Setup

1. **Copy the environment template**:
   ```bash
   cp .env.example .env
   ```

2. **Update database URL for local development**:
   ```bash
   DATABASE_URL=postgresql://postgres:postgres@localhost:5432/izy-mercado-db?sslmode=disable
   ```

3. **For SSL testing with Cloud SQL**:
   ```bash
   # Add to .env file
   DATABASE_SSL_SERVER_CA=-----BEGIN CERTIFICATE-----
   ...your actual server CA certificate...
   -----END CERTIFICATE-----
   ```

## How SSL Works

### Go Application
1. Detects `DATABASE_SSL_SERVER_CA` environment variable
2. Adds `sslmode=verify-ca` to connection string
3. Creates TLS config with raw certificate content
4. Uses custom certificate verification for Cloud SQL compatibility
5. Supports client certificates for mutual TLS

### Migration Script
1. Detects `DATABASE_SSL_SERVER_CA` environment variable
2. Adds `sslmode=require` to connection string
3. Enables SSL encryption for psql and migrate tools
4. Note: Limited certificate verification due to tool limitations

## Security Considerations

- **Go Application**: Full certificate verification with verify-ca mode
- **Migration Tools**: SSL encryption with require mode (best available for these tools)
- **Production**: All certificates managed through Google Cloud Secret Manager
- **Development**: Can use local certificates or disable SSL for local PostgreSQL

## Troubleshooting

### Common Issues

1. **Go app works, migration fails**: This is expected - migration tools use require mode
2. **Certificate format errors**: Ensure certificates are in proper PEM format
3. **Connection failures**: Check Cloud SQL instance SSL configuration

### Testing

```bash
# Test Go application SSL
export DATABASE_SSL_SERVER_CA="$(cat server-ca.pem)"
go run ./cmd

# Test migration script SSL
export DATABASE_SSL_SERVER_CA="$(cat server-ca.pem)"
./scripts/migrate.sh
```

## Migration from Non-SSL

1. Add `DATABASE_SSL_SERVER_CA` secret with your server CA certificate
2. Deploy the updated application
3. Both Go app and migration script will automatically enable SSL
4. Go app uses verify-ca, migration script uses require mode
