# PostgreSQL SSL Configuration with Google Cloud Secret Manager

This document explains how to configure SSL connections to your Cloud SQL PostgreSQL instance using certificates stored in Google Cloud Secret Manager.

## Overview

The application now supports secure SSL connections to PostgreSQL using:
- Server certificate verification (always uses "verify-ca" mode)
- Client certificate authentication (optional)
- Certificates loaded from Google Cloud Secret Manager as raw text (not file paths)

## SSL Mode

The application automatically uses **"verify-ca"** SSL mode when server CA certificates are provided. No manual SSL mode configuration is required.

## Setting up SSL Certificates in Google Cloud Secret Manager

### 1. Create the SSL Secrets

You need to create the following secrets in Google Cloud Secret Manager:

```bash
# Server CA Certificate (required for SSL connections)
gcloud secrets create DATABASE_SSL_SERVER_CA --data-file=server-ca.pem

# Client Certificate (optional, for client authentication)
gcloud secrets create DATABASE_SSL_CLIENT_CERT --data-file=client-cert.pem

# Client Private Key (required if client certificate is provided)
gcloud secrets create DATABASE_SSL_CLIENT_KEY --data-file=client-key.pem
```

### 2. Certificate Format Requirements

All certificates must be in PEM format:

**Server CA Certificate:**
```
-----BEGIN CERTIFICATE-----
MIIDQTCCAimgAwIBAgITBmyfz5m/jAo54vB4ikPmljZbyjANBgkqhkiG9w0BAQsF
...certificate content...
-----END CERTIFICATE-----
```

**Client Certificate:**
```
-----BEGIN CERTIFICATE-----
MIIDQTCCAimgAwIBAgITBmyfz5m/jAo54vB4ikPmljZbyjANBgkqhkiG9w0BAQsF
...certificate content...
-----END CERTIFICATE-----
```

**Client Private Key:**
```
-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCyeIBxynj
...key content...
-----END PRIVATE KEY-----
```

### 3. Obtaining Certificates from Cloud SQL

To get the certificates from your Cloud SQL instance:

```bash
# Get the server CA certificate
gcloud sql ssl-certs describe server-ca-cert --instance=YOUR_INSTANCE_NAME --format="value(cert)" > server-ca.pem

# Create a client certificate (if needed)
gcloud sql ssl-certs create client-cert client-key.pem --instance=YOUR_INSTANCE_NAME

# Download the client certificate
gcloud sql ssl-certs describe client-cert --instance=YOUR_INSTANCE_NAME --format="value(cert)" > client-cert.pem
```

### 4. Grant Access to Secrets

Ensure your Cloud Run service account has access to the secrets:

```bash
# Grant access to all SSL secrets
for secret in DATABASE_SSL_SERVER_CA DATABASE_SSL_CLIENT_CERT DATABASE_SSL_CLIENT_KEY; do
  gcloud secrets add-iam-policy-binding $secret \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/secretmanager.secretAccessor"
done
```

## Configuration

The application automatically loads SSL configuration from environment variables:

- `DATABASE_SSL_SERVER_CA`: Server CA certificate in PEM format (required for SSL)
- `DATABASE_SSL_CLIENT_CERT`: Client certificate in PEM format (optional)
- `DATABASE_SSL_CLIENT_KEY`: Client private key in PEM format (optional)

**SSL Mode**: Automatically set to "verify-ca" when `DATABASE_SSL_SERVER_CA` is provided.

## Deployment

The SSL certificates are automatically injected into the Cloud Run service through the deployment workflow. The secrets are already configured in `.github/workflows/deploy.production.yml`.

## Connection Pool Configuration

The SSL configuration is applied to the PostgreSQL connection pool with the following settings:
- MaxConns: 20
- MinConns: 5
- SSL: verify-ca (when certificates are provided)

## Logging

The application provides detailed logging for SSL configuration:

```
INFO SSL configuration applied to database connection ssl_mode=verify-ca
INFO Server CA certificate loaded successfully
INFO Client certificate and key loaded successfully
INFO TLS configuration created successfully
```

## Troubleshooting

### Common Issues

1. **Certificate format errors**: Ensure certificates are in proper PEM format with correct headers and footers.

2. **Missing client key**: If you provide a client certificate, you must also provide the corresponding private key.

3. **Connection failures**: Check that your Cloud SQL instance is configured to require SSL connections and that the certificates match.

### Testing SSL Configuration

You can test the SSL configuration locally by setting the environment variables:

```bash
export DATABASE_SSL_SERVER_CA="$(cat server-ca.pem)"
export DATABASE_SSL_CLIENT_CERT="$(cat client-cert.pem)"
export DATABASE_SSL_CLIENT_KEY="$(cat client-key.pem)"
```

## Security Best Practices

1. **Always use SSL** in production environments
2. **Rotate certificates regularly** as per your security policy
3. **Monitor certificate expiration** and renew before expiry
4. **Use client certificates** for additional authentication layer
5. **Restrict network access** to your Cloud SQL instance using authorized networks or private IP

## Migration from Non-SSL

If you're migrating from a non-SSL setup:

1. Add the `DATABASE_SSL_SERVER_CA` secret with your server CA certificate
2. Deploy the updated application
3. The application will automatically detect the certificate and enable SSL with verify-ca mode
4. Update your Cloud SQL instance to require SSL connections
